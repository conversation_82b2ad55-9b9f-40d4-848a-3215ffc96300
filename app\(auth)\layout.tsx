import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="relative min-h-screen">
      <Button className="fixed top-5 left-5 z-10" variant={"outline"} asChild>
        <Link href={"/"}>
          <ArrowLeft className="h-4 w-4" />
          Back
        </Link>
      </Button>
      {children}
    </div>
  );
}
